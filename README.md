# ERP PME avec Docker et PostgreSQL

Un système ERP (Enterprise Resource Planning) pour les PME, avec une interface inspirée de SAP, utilisant Docker et PostgreSQL pour un déploiement facile et portable.

## Architecture

Ce projet utilise une architecture moderne avec :

- **Frontend** : React avec Vite
- **Backend** : Node.js avec Express
- **Base de données** : PostgreSQL
- **Conteneurisation** : Docker et Docker Compose

## Fonctionnalités

- Gestion des articles (produits)
- Gestion des documents liés aux articles
- Interface utilisateur inspirée de SAP
- Base de données PostgreSQL

## Technologies utilisées

- React 18
- Vite
- React Router
- Node.js et Express
- PostgreSQL
- Docker et Docker Compose
- Flatpickr (pour les sélecteurs de date)
- CSS personnalisé (style SAP)

## Prérequis

- [Docker](https://www.docker.com/products/docker-desktop/) installé sur votre machine
- [Docker Compose](https://docs.docker.com/compose/install/) (généralement inclus avec Docker Desktop)

## Installation et démarrage

1. Clonez ce dépôt
2. Lancez l'application avec Docker Compose :
   ```bash
   docker-compose up
   ```
3. Accédez à l'application :
   - Frontend : http://localhost:3000
   - API : http://localhost:3001
   - Base de données PostgreSQL : localhost:5432 (accessible via un client PostgreSQL)

## Structure du projet

```
erp-pme/
├── api/                  # Backend Express
│   ├── src/              # Code source du backend
│   ├── Dockerfile        # Configuration Docker pour le backend
│   └── package.json      # Dépendances du backend
├── web/                  # Frontend React
│   ├── src/              # Code source du frontend
│   ├── public/           # Fichiers statiques
│   ├── Dockerfile        # Configuration Docker pour le frontend
│   └── package.json      # Dépendances du frontend
├── database/
│   └── init.sql          # Script d'initialisation SQL
├── docker-compose.yml    # Configuration Docker Compose
└── README.md             # Documentation
```

## Structure de la base de données

Voici les tables créées dans la base de données PostgreSQL :

### Table `articles`

| Colonne | Type | Description |
|---------|------|-------------|
| id | text | Identifiant unique de l'article (clé primaire) |
| designation | text | Nom de l'article |
| division | text | Code de la division |
| prix | numeric | Prix de l'article |
| description | text | Description détaillée |
| unite | text | Unité de mesure |
| stockMin | integer | Stock minimum |
| stockMax | integer | Stock maximum |
| fournisseurPrincipal | text | Code du fournisseur principal |

### Table `magasins`

| Colonne | Type | Description |
|---------|------|-------------|
| code | text | Code du magasin (clé primaire) |
| nom | text | Nom du magasin |
| ville | text | Ville du magasin |

### Table `fournisseurs`

| Colonne | Type | Description |
|---------|------|-------------|
| code | text | Code du fournisseur (clé primaire) |
| nom | text | Nom du fournisseur |
| contact | text | Nom du contact |

### Table `clients`

| Colonne | Type | Description |
|---------|------|-------------|
| code | text | Code du client (clé primaire) |
| nom | text | Nom du client |
| ville | text | Ville du client |

### Table `codes_mouvement`

| Colonne | Type | Description |
|---------|------|-------------|
| code | text | Code du mouvement (clé primaire) |
| libelle | text | Libellé du mouvement |
| type | text | Type de mouvement (entrée, sortie, etc.) |

### Table `stocks_speciaux`

| Colonne | Type | Description |
|---------|------|-------------|
| code | text | Code du stock spécial (clé primaire) |
| libelle | text | Libellé du stock spécial |

## Développement

### Modification du frontend

Le code source du frontend se trouve dans le dossier `web/src/`. Les modifications sont automatiquement prises en compte grâce au hot-reloading de Vite.

### Modification du backend

Le code source du backend se trouve dans le dossier `api/src/`. Les modifications sont automatiquement prises en compte grâce à Nodemon.

### Modification de la base de données

Pour modifier la structure de la base de données, vous pouvez :
1. Modifier le fichier `database/init.sql`
2. Redémarrer les conteneurs avec `docker-compose down` puis `docker-compose up`

## Accès à la base de données

Vous pouvez vous connecter à la base de données PostgreSQL avec les informations suivantes :
- Hôte : localhost
- Port : 5432
- Base de données : erp_db
- Utilisateur : erp_user
- Mot de passe : erp_password

## Arrêt de l'application

Pour arrêter l'application, utilisez :
```bash
docker-compose down
```

Pour arrêter l'application et supprimer les volumes (ce qui effacera les données de la base de données) :
```bash
docker-compose down -v
```

## Licence

Ce projet est sous licence MIT.
