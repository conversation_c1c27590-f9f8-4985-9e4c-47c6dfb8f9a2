const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');
require('dotenv').config();

// Initialisation de l'application Express
const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la connexion à la base de données
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Test de connexion à la base de données
pool.query('SELECT NOW()', (err, res) => {
  if (err) {
    console.error('Erreur de connexion à la base de données:', err);
  } else {
    console.log('Connexion à la base de données établie:', res.rows[0].now);
  }
});

// Routes de base
app.get('/', (req, res) => {
  res.json({ message: 'API ERP PME opérationnelle' });
});

// Routes pour les articles
app.get('/api/articles', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM articles ORDER BY id');
    res.json(result.rows);
  } catch (err) {
    console.error('Erreur lors de la récupération des articles:', err);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

app.get('/api/articles/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('SELECT * FROM articles WHERE id = $1', [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Article non trouvé' });
    }

    res.json(result.rows[0]);
  } catch (err) {
    console.error(`Erreur lors de la récupération de l'article ${req.params.id}:`, err);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

app.post('/api/articles', async (req, res) => {
  try {
    const { id, designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal } = req.body;

    // Vérifier si l'article existe déjà
    const checkResult = await pool.query('SELECT id FROM articles WHERE id = $1', [id]);

    if (checkResult.rows.length > 0) {
      return res.status(400).json({ error: 'Un article avec cet ID existe déjà' });
    }

    const result = await pool.query(
      'INSERT INTO articles (id, designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *',
      [id, designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal]
    );

    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Erreur lors de la création de l\'article:', err);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

app.put('/api/articles/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal } = req.body;

    const result = await pool.query(
      'UPDATE articles SET designation = $1, division = $2, prix = $3, description = $4, unite = $5, stockMin = $6, stockMax = $7, fournisseurPrincipal = $8 WHERE id = $9 RETURNING *',
      [designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Article non trouvé' });
    }

    res.json(result.rows[0]);
  } catch (err) {
    console.error(`Erreur lors de la mise à jour de l'article ${req.params.id}:`, err);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

app.delete('/api/articles/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query('DELETE FROM articles WHERE id = $1 RETURNING *', [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Article non trouvé' });
    }

    res.json({ message: 'Article supprimé avec succès' });
  } catch (err) {
    console.error(`Erreur lors de la suppression de l'article ${req.params.id}:`, err);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Routes pour les magasins
app.get('/api/magasins', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM magasins ORDER BY code');
    res.json(result.rows);
  } catch (err) {
    console.error('Erreur lors de la récupération des magasins:', err);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Routes pour les fournisseurs
app.get('/api/fournisseurs', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM fournisseurs ORDER BY code');
    res.json(result.rows);
  } catch (err) {
    console.error('Erreur lors de la récupération des fournisseurs:', err);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Routes pour les clients
app.get('/api/clients', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM clients ORDER BY code');
    res.json(result.rows);
  } catch (err) {
    console.error('Erreur lors de la récupération des clients:', err);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Routes pour les codes de mouvement
app.get('/api/codes-mouvement', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM codes_mouvement ORDER BY code');
    res.json(result.rows);
  } catch (err) {
    console.error('Erreur lors de la récupération des codes de mouvement:', err);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Routes pour les stocks spéciaux
app.get('/api/stocks-speciaux', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM stocks_speciaux ORDER BY code');
    res.json(result.rows);
  } catch (err) {
    console.error('Erreur lors de la récupération des stocks spéciaux:', err);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Démarrage du serveur
app.listen(port, () => {
  console.log(`Serveur API démarré sur http://localhost:${port}`);
});
