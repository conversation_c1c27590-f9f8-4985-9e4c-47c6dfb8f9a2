version: '3.8'

services:
  # Service de base de données PostgreSQL
  db:
    image: postgres:14
    restart: always
    environment:
      POSTGRES_PASSWORD: erp_password
      POSTGRES_USER: erp_user
      POSTGRES_DB: erp_db
    ports:
      - "5432:5432"
    volumes:
      - ./database/data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - erp-network

  # Service API backend
  api:
    build: ./api
    ports:
      - "3001:3001"
    depends_on:
      - db
    environment:
      DATABASE_URL: ****************************************/erp_db
      NODE_ENV: development
    volumes:
      - ./api:/app
      - /app/node_modules
    networks:
      - erp-network

  # Service frontend
  web:
    build: ./web
    ports:
      - "3000:3000"
    depends_on:
      - api
    volumes:
      - ./web:/app
      - /app/node_modules
    networks:
      - erp-network

networks:
  erp-network:
    driver: bridge

volumes:
  postgres-data:
