document.addEventListener('DOMContentLoaded', function() {
    // Éléments DOM
    const navLinks = document.querySelectorAll('nav a');
    const sections = document.querySelectorAll('.section');
    const articlesTable = document.getElementById('articles-table').querySelector('tbody');
    const clientsTable = document.getElementById('clients-table').querySelector('tbody');
    const fournisseursTable = document.getElementById('fournisseurs-table').querySelector('tbody');
    const articleForm = document.getElementById('article-form');
    const formTitle = document.getElementById('form-title');
    const btnNewArticle = document.getElementById('btn-new-article');
    const btnCancel = document.getElementById('btn-cancel');
    const fournisseurSelect = document.getElementById('article-fournisseur');
    
    // État de l'application
    let currentArticleId = null;
    let isEditMode = false;
    
    // Navigation
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Mettre à jour les classes actives
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // Afficher la section correspondante
            const targetId = this.id.replace('nav-', '') + '-section';
            sections.forEach(section => {
                section.classList.remove('active');
                if (section.id === targetId) {
                    section.classList.add('active');
                }
            });
            
            // Charger les données si nécessaire
            if (targetId === 'articles-section') {
                loadArticles();
            } else if (targetId === 'clients-section') {
                loadClients();
            } else if (targetId === 'fournisseurs-section') {
                loadFournisseurs();
            }
        });
    });
    
    // Charger les articles au démarrage
    loadArticles();
    loadFournisseurs();
    
    // Événements pour le formulaire d'article
    btnNewArticle.addEventListener('click', function() {
        showArticleForm(true);
    });
    
    btnCancel.addEventListener('click', function() {
        hideArticleForm();
    });
    
    articleForm.addEventListener('submit', function(e) {
        e.preventDefault();
        saveArticle();
    });
    
    // Fonctions pour charger les données
    function loadArticles() {
        fetch('/api/articles')
            .then(response => response.json())
            .then(articles => {
                articlesTable.innerHTML = '';
                
                if (articles.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = '<td colspan="5" class="text-center">Aucun article trouvé</td>';
                    articlesTable.appendChild(row);
                    return;
                }
                
                articles.forEach(article => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${article.id}</td>
                        <td>${article.designation}</td>
                        <td>${article.division}</td>
                        <td>${parseFloat(article.prix).toFixed(2)} €</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-edit" data-id="${article.id}">Modifier</button>
                                <button class="btn btn-danger btn-delete" data-id="${article.id}">Supprimer</button>
                            </div>
                        </td>
                    `;
                    articlesTable.appendChild(row);
                });
                
                // Ajouter les événements pour les boutons d'action
                document.querySelectorAll('.btn-edit').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const id = this.getAttribute('data-id');
                        editArticle(id);
                    });
                });
                
                document.querySelectorAll('.btn-delete').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const id = this.getAttribute('data-id');
                        deleteArticle(id);
                    });
                });
            })
            .catch(error => {
                console.error('Erreur lors du chargement des articles:', error);
                alert('Erreur lors du chargement des articles');
            });
    }
    
    function loadClients() {
        fetch('/api/clients')
            .then(response => response.json())
            .then(clients => {
                clientsTable.innerHTML = '';
                
                if (clients.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = '<td colspan="3" class="text-center">Aucun client trouvé</td>';
                    clientsTable.appendChild(row);
                    return;
                }
                
                clients.forEach(client => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${client.code}</td>
                        <td>${client.nom}</td>
                        <td>${client.ville || '-'}</td>
                    `;
                    clientsTable.appendChild(row);
                });
            })
            .catch(error => {
                console.error('Erreur lors du chargement des clients:', error);
                alert('Erreur lors du chargement des clients');
            });
    }
    
    function loadFournisseurs() {
        fetch('/api/fournisseurs')
            .then(response => response.json())
            .then(fournisseurs => {
                // Remplir la table des fournisseurs
                if (document.getElementById('fournisseurs-section').classList.contains('active')) {
                    fournisseursTable.innerHTML = '';
                    
                    if (fournisseurs.length === 0) {
                        const row = document.createElement('tr');
                        row.innerHTML = '<td colspan="3" class="text-center">Aucun fournisseur trouvé</td>';
                        fournisseursTable.appendChild(row);
                        return;
                    }
                    
                    fournisseurs.forEach(fournisseur => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${fournisseur.code}</td>
                            <td>${fournisseur.nom}</td>
                            <td>${fournisseur.contact || '-'}</td>
                        `;
                        fournisseursTable.appendChild(row);
                    });
                }
                
                // Remplir le select des fournisseurs dans le formulaire d'article
                fournisseurSelect.innerHTML = '<option value="">Sélectionnez un fournisseur</option>';
                fournisseurs.forEach(fournisseur => {
                    const option = document.createElement('option');
                    option.value = fournisseur.code;
                    option.textContent = `${fournisseur.code} - ${fournisseur.nom}`;
                    fournisseurSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Erreur lors du chargement des fournisseurs:', error);
                alert('Erreur lors du chargement des fournisseurs');
            });
    }
    
    // Fonctions pour gérer le formulaire d'article
    function showArticleForm(isNew) {
        isEditMode = !isNew;
        formTitle.textContent = isNew ? 'Nouvel Article' : 'Modifier Article';
        
        // Réinitialiser le formulaire si c'est un nouvel article
        if (isNew) {
            articleForm.reset();
            document.getElementById('article-id').disabled = false;
            currentArticleId = null;
        } else {
            document.getElementById('article-id').disabled = true;
        }
        
        // Afficher le formulaire
        document.getElementById('articles-section').classList.remove('active');
        document.getElementById('article-form-section').classList.add('active');
    }
    
    function hideArticleForm() {
        document.getElementById('article-form-section').classList.remove('active');
        document.getElementById('articles-section').classList.add('active');
        articleForm.reset();
    }
    
    function editArticle(id) {
        fetch(`/api/articles/${id}`)
            .then(response => response.json())
            .then(article => {
                currentArticleId = article.id;
                
                // Remplir le formulaire avec les données de l'article
                document.getElementById('article-id').value = article.id;
                document.getElementById('article-designation').value = article.designation;
                document.getElementById('article-division').value = article.division;
                document.getElementById('article-prix').value = article.prix;
                document.getElementById('article-description').value = article.description || '';
                document.getElementById('article-unite').value = article.unite || '';
                document.getElementById('article-stockMin').value = article.stockMin || 0;
                document.getElementById('article-stockMax').value = article.stockMax || 0;
                document.getElementById('article-fournisseur').value = article.fournisseurPrincipal || '';
                
                showArticleForm(false);
            })
            .catch(error => {
                console.error(`Erreur lors de la récupération de l'article ${id}:`, error);
                alert('Erreur lors de la récupération de l\'article');
            });
    }
    
    function saveArticle() {
        const formData = new FormData(articleForm);
        const article = {};
        
        formData.forEach((value, key) => {
            if (key === 'prix' || key === 'stockMin' || key === 'stockMax') {
                article[key] = parseFloat(value) || 0;
            } else {
                article[key] = value;
            }
        });
        
        const url = isEditMode ? `/api/articles/${currentArticleId}` : '/api/articles';
        const method = isEditMode ? 'PUT' : 'POST';
        
        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(article)
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erreur lors de l\'enregistrement de l\'article');
                }
                return response.json();
            })
            .then(() => {
                hideArticleForm();
                loadArticles();
                alert(isEditMode ? 'Article modifié avec succès' : 'Article créé avec succès');
            })
            .catch(error => {
                console.error('Erreur lors de l\'enregistrement de l\'article:', error);
                alert('Erreur lors de l\'enregistrement de l\'article');
            });
    }
    
    function deleteArticle(id) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cet article ?')) {
            fetch(`/api/articles/${id}`, {
                method: 'DELETE'
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Erreur lors de la suppression de l\'article');
                    }
                    return response.json();
                })
                .then(() => {
                    loadArticles();
                    alert('Article supprimé avec succès');
                })
                .catch(error => {
                    console.error(`Erreur lors de la suppression de l'article ${id}:`, error);
                    alert('Erreur lors de la suppression de l\'article');
                });
        }
    }
});
