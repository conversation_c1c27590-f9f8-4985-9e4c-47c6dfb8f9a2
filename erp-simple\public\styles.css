:root {
    --sap-blue: #366092;
    --sap-light-blue: #5b9bd5;
    --sap-gray: #a5a5a5;
    --sap-dark-gray: #333;
    --sap-bg: #f0f0f0;
    --sap-border: #d1d1d1;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: var(--sap-bg);
    color: var(--sap-dark-gray);
    font-size: 14px;
    line-height: 1.5;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    background-color: var(--sap-blue);
    color: white;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
}

nav ul {
    display: flex;
    list-style: none;
}

nav li {
    margin: 0 10px;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.3s;
}

nav a:hover, nav a.active {
    background-color: rgba(255, 255, 255, 0.2);
}

main {
    flex: 1;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

footer {
    background-color: var(--sap-dark-gray);
    color: white;
    text-align: center;
    padding: 10px;
}

.card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    padding: 15px;
    background-color: var(--sap-light-blue);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.2rem;
    margin: 0;
}

.card-body {
    padding: 15px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th, table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--sap-border);
}

table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.btn {
    display: inline-block;
    padding: 8px 15px;
    background-color: #f5f5f5;
    color: var(--sap-dark-gray);
    border: 1px solid var(--sap-border);
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #e5e5e5;
}

.btn-primary {
    background-color: var(--sap-light-blue);
    color: white;
    border-color: var(--sap-light-blue);
}

.btn-primary:hover {
    background-color: var(--sap-blue);
}

.btn-danger {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.form-group {
    flex: 1;
    min-width: 250px;
    padding: 0 10px;
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input, select, textarea {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid var(--sap-border);
    border-radius: 3px;
    font-size: 14px;
}

textarea {
    resize: vertical;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

@media (max-width: 768px) {
    .form-group {
        min-width: 100%;
    }
}
