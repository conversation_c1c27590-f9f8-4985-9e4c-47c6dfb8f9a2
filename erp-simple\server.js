const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Initialisation de l'application Express
const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Création et initialisation de la base de données SQLite
const dbPath = path.join(__dirname, 'erp.db');
const db = new sqlite3.Database(dbPath);

// Création des tables
db.serialize(() => {
  // Table articles
  db.run(`CREATE TABLE IF NOT EXISTS articles (
    id TEXT PRIMARY KEY,
    designation TEXT NOT NULL,
    division TEXT NOT NULL,
    prix REAL NOT NULL,
    description TEXT,
    unite TEXT,
    stockMin INTEGER DEFAULT 0,
    stockMax INTEGER DEFAULT 0,
    fournisseurPrincipal TEXT
  )`);

  // Table magasins
  db.run(`CREATE TABLE IF NOT EXISTS magasins (
    code TEXT PRIMARY KEY,
    nom TEXT NOT NULL,
    ville TEXT
  )`);

  // Table fournisseurs
  db.run(`CREATE TABLE IF NOT EXISTS fournisseurs (
    code TEXT PRIMARY KEY,
    nom TEXT NOT NULL,
    contact TEXT
  )`);

  // Table clients
  db.run(`CREATE TABLE IF NOT EXISTS clients (
    code TEXT PRIMARY KEY,
    nom TEXT NOT NULL,
    ville TEXT
  )`);

  // Insertion des données de test si les tables sont vides
  db.get("SELECT COUNT(*) as count FROM articles", (err, row) => {
    if (err) {
      console.error("Erreur lors de la vérification des articles:", err);
      return;
    }
    
    if (row.count === 0) {
      // Insertion des articles
      const articles = [
        { id: "6015379", designation: "Produit A", division: "9505", prix: 125.50, description: "Description du produit A", unite: "PCE", stockMin: 10, stockMax: 100, fournisseurPrincipal: "F001" },
        { id: "6015380", designation: "Produit B", division: "9505", prix: 89.99, description: "Description du produit B", unite: "KG", stockMin: 5, stockMax: 50, fournisseurPrincipal: "F002" },
        { id: "6015381", designation: "Produit C", division: "9602", prix: 45.20, description: "Description du produit C", unite: "L", stockMin: 20, stockMax: 200, fournisseurPrincipal: "F001" },
        { id: "6015382", designation: "Produit D", division: "9602", prix: 210.00, description: "Description du produit D", unite: "PCE", stockMin: 2, stockMax: 30, fournisseurPrincipal: "F003" }
      ];
      
      const stmt = db.prepare("INSERT INTO articles (id, designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
      
      articles.forEach(article => {
        stmt.run(article.id, article.designation, article.division, article.prix, article.description, article.unite, article.stockMin, article.stockMax, article.fournisseurPrincipal);
      });
      
      stmt.finalize();
      console.log("Données d'articles insérées avec succès");
    }
  });

  db.get("SELECT COUNT(*) as count FROM magasins", (err, row) => {
    if (err || row.count === 0) {
      // Insertion des magasins
      const magasins = [
        { code: "M001", nom: "Magasin Principal", ville: "Paris" },
        { code: "M002", nom: "Magasin Est", ville: "Lyon" },
        { code: "M003", nom: "Magasin Ouest", ville: "Nantes" }
      ];
      
      const stmt = db.prepare("INSERT INTO magasins (code, nom, ville) VALUES (?, ?, ?)");
      
      magasins.forEach(magasin => {
        stmt.run(magasin.code, magasin.nom, magasin.ville);
      });
      
      stmt.finalize();
      console.log("Données de magasins insérées avec succès");
    }
  });

  db.get("SELECT COUNT(*) as count FROM fournisseurs", (err, row) => {
    if (err || row.count === 0) {
      // Insertion des fournisseurs
      const fournisseurs = [
        { code: "F001", nom: "Fournisseur Alpha", contact: "M. Dupont" },
        { code: "F002", nom: "Fournisseur Beta", contact: "Mme Martin" },
        { code: "F003", nom: "Fournisseur Gamma", contact: "M. Leroy" }
      ];
      
      const stmt = db.prepare("INSERT INTO fournisseurs (code, nom, contact) VALUES (?, ?, ?)");
      
      fournisseurs.forEach(fournisseur => {
        stmt.run(fournisseur.code, fournisseur.nom, fournisseur.contact);
      });
      
      stmt.finalize();
      console.log("Données de fournisseurs insérées avec succès");
    }
  });

  db.get("SELECT COUNT(*) as count FROM clients", (err, row) => {
    if (err || row.count === 0) {
      // Insertion des clients
      const clients = [
        { code: "C001", nom: "Client Premium", ville: "Marseille" },
        { code: "C002", nom: "Client Standard", ville: "Toulouse" },
        { code: "C003", nom: "Client VIP", ville: "Lille" }
      ];
      
      const stmt = db.prepare("INSERT INTO clients (code, nom, ville) VALUES (?, ?, ?)");
      
      clients.forEach(client => {
        stmt.run(client.code, client.nom, client.ville);
      });
      
      stmt.finalize();
      console.log("Données de clients insérées avec succès");
    }
  });
});

// Routes API
app.get('/api/articles', (req, res) => {
  db.all("SELECT * FROM articles ORDER BY id", (err, rows) => {
    if (err) {
      console.error("Erreur lors de la récupération des articles:", err);
      return res.status(500).json({ error: "Erreur serveur" });
    }
    res.json(rows);
  });
});

app.get('/api/articles/:id', (req, res) => {
  const { id } = req.params;
  db.get("SELECT * FROM articles WHERE id = ?", [id], (err, row) => {
    if (err) {
      console.error(`Erreur lors de la récupération de l'article ${id}:`, err);
      return res.status(500).json({ error: "Erreur serveur" });
    }
    if (!row) {
      return res.status(404).json({ error: "Article non trouvé" });
    }
    res.json(row);
  });
});

app.post('/api/articles', (req, res) => {
  const { id, designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal } = req.body;
  
  db.run(
    "INSERT INTO articles (id, designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
    [id, designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal],
    function(err) {
      if (err) {
        console.error("Erreur lors de la création de l'article:", err);
        return res.status(500).json({ error: "Erreur serveur" });
      }
      
      db.get("SELECT * FROM articles WHERE id = ?", [id], (err, row) => {
        if (err) {
          console.error("Erreur lors de la récupération de l'article créé:", err);
          return res.status(500).json({ error: "Erreur serveur" });
        }
        res.status(201).json(row);
      });
    }
  );
});

app.put('/api/articles/:id', (req, res) => {
  const { id } = req.params;
  const { designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal } = req.body;
  
  db.run(
    "UPDATE articles SET designation = ?, division = ?, prix = ?, description = ?, unite = ?, stockMin = ?, stockMax = ?, fournisseurPrincipal = ? WHERE id = ?",
    [designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal, id],
    function(err) {
      if (err) {
        console.error(`Erreur lors de la mise à jour de l'article ${id}:`, err);
        return res.status(500).json({ error: "Erreur serveur" });
      }
      
      if (this.changes === 0) {
        return res.status(404).json({ error: "Article non trouvé" });
      }
      
      db.get("SELECT * FROM articles WHERE id = ?", [id], (err, row) => {
        if (err) {
          console.error("Erreur lors de la récupération de l'article mis à jour:", err);
          return res.status(500).json({ error: "Erreur serveur" });
        }
        res.json(row);
      });
    }
  );
});

app.delete('/api/articles/:id', (req, res) => {
  const { id } = req.params;
  
  db.run("DELETE FROM articles WHERE id = ?", [id], function(err) {
    if (err) {
      console.error(`Erreur lors de la suppression de l'article ${id}:`, err);
      return res.status(500).json({ error: "Erreur serveur" });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: "Article non trouvé" });
    }
    
    res.json({ message: "Article supprimé avec succès" });
  });
});

// Routes pour les magasins, fournisseurs et clients
app.get('/api/magasins', (req, res) => {
  db.all("SELECT * FROM magasins ORDER BY code", (err, rows) => {
    if (err) {
      console.error("Erreur lors de la récupération des magasins:", err);
      return res.status(500).json({ error: "Erreur serveur" });
    }
    res.json(rows);
  });
});

app.get('/api/fournisseurs', (req, res) => {
  db.all("SELECT * FROM fournisseurs ORDER BY code", (err, rows) => {
    if (err) {
      console.error("Erreur lors de la récupération des fournisseurs:", err);
      return res.status(500).json({ error: "Erreur serveur" });
    }
    res.json(rows);
  });
});

app.get('/api/clients', (req, res) => {
  db.all("SELECT * FROM clients ORDER BY code", (err, rows) => {
    if (err) {
      console.error("Erreur lors de la récupération des clients:", err);
      return res.status(500).json({ error: "Erreur serveur" });
    }
    res.json(rows);
  });
});

// Route pour servir l'application frontend
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Démarrage du serveur
app.listen(port, () => {
  console.log(`Serveur démarré sur http://localhost:${port}`);
});
