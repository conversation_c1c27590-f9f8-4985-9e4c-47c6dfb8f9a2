import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/layout/Layout';
import ArticleList from './components/modules/articles/ArticleList';
import ArticleForm from './components/modules/articles/ArticleForm';
import ArticleDocuments from './components/modules/articles/ArticleDocuments';
import './styles/SapTheme.css';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Navigate to="/articles" replace />} />
          <Route path="articles" element={<ArticleList />} />
          <Route path="articles/:id" element={<ArticleForm />} />
          <Route path="articles/:id/documents" element={<ArticleDocuments />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;
