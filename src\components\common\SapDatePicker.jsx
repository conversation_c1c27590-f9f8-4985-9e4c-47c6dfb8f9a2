import React, { useEffect, useRef } from 'react';
import '../../styles/SapTheme.css';

const SapDatePicker = ({ id, value, onChange, placeholder = 'JJ.MM.AAAA' }) => {
  const dateInputRef = useRef(null);
  
  useEffect(() => {
    // Cette fonction serait appelée après le chargement du composant
    // pour initialiser flatpickr sur l'élément input
    if (dateInputRef.current && window.flatpickr) {
      const fp = window.flatpickr(dateInputRef.current, {
        dateFormat: 'd.m.Y',
        allowInput: true,
        locale: 'fr',
        onChange: (selectedDates, dateStr) => {
          if (onChange) onChange(dateStr);
        }
      });
      
      // Stocker l'instance flatpickr pour y accéder plus tard
      dateInputRef.current._flatpickr = fp;
    }
    
    return () => {
      // Nettoyage lors du démontage du composant
      if (dateInputRef.current && dateInputRef.current._flatpickr) {
        dateInputRef.current._flatpickr.destroy();
      }
    };
  }, []);
  
  return (
    <div className="sap-date-container">
      <input
        type="text"
        id={id}
        className="sap-date-input"
        placeholder={placeholder}
        value={value || ''}
        onChange={(e) => onChange && onChange(e.target.value)}
        ref={dateInputRef}
      />
      <div 
        className="sap-date-trigger" 
        onClick={() => dateInputRef.current && dateInputRef.current._flatpickr && dateInputRef.current._flatpickr.open()}
      >
        📅
      </div>
    </div>
  );
};

export default SapDatePicker;
