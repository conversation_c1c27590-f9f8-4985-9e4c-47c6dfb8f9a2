.erp-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.erp-header {
  background-color: var(--sap-blue);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.erp-logo {
  font-size: 1.5em;
  font-weight: bold;
}

.erp-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.erp-nav li {
  margin: 0 10px;
}

.erp-nav a {
  color: white;
  text-decoration: none;
  padding: 5px 10px;
  border-radius: 3px;
  transition: background-color 0.3s;
}

.erp-nav a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.erp-user {
  display: flex;
  align-items: center;
}

.erp-user-name {
  margin-right: 10px;
}

.erp-logout-btn {
  background-color: transparent;
  border: 1px solid white;
  color: white;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.erp-logout-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.erp-main {
  flex: 1;
  padding: 20px;
  background-color: var(--sap-bg);
}

.erp-footer {
  background-color: var(--sap-dark-gray);
  color: white;
  text-align: center;
  padding: 10px;
  font-size: 0.9em;
}

/* Style pour les tableaux */
.sap-table-container {
  overflow-x: auto;
  margin-bottom: 15px;
}

.sap-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid var(--sap-border);
}

.sap-table th {
  background-color: #f5f5f5;
  padding: 8px 12px;
  text-align: left;
  font-weight: bold;
  border-bottom: 1px solid var(--sap-border);
}

.sap-table td {
  padding: 8px 12px;
  border-bottom: 1px solid var(--sap-border);
}

.sap-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.sap-table tr:hover {
  background-color: #f0f0f0;
}
