import React from 'react';
import { Outlet, Link } from 'react-router-dom';
import './Layout.css';

const Layout = () => {
  return (
    <div className="erp-layout">
      <header className="erp-header">
        <div className="erp-logo">ERP PME</div>
        <nav className="erp-nav">
          <ul>
            <li><Link to="/articles">Articles</Link></li>
            <li><Link to="/clients">Clients</Link></li>
            <li><Link to="/fournisseurs">Fournisseurs</Link></li>
            <li><Link to="/stock">Stock</Link></li>
          </ul>
        </nav>
        <div className="erp-user">
          <span className="erp-user-name">Admin</span>
          <button className="erp-logout-btn">Déconnexion</button>
        </div>
      </header>
      
      <main className="erp-main">
        <Outlet />
      </main>
      
      <footer className="erp-footer">
        <p>&copy; 2023 ERP PME - Tous droits réservés</p>
      </footer>
    </div>
  );
};

export default Layout;
