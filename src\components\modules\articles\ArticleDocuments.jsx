import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import SapContainer from '../../common/SapContainer';
import SapBlock from '../../common/SapBlock';
import SapField from '../../common/SapField';
import SapFieldRow from '../../common/SapFieldRow';
import SapButton from '../../common/SapButton';
import SapDatePicker from '../../common/SapDatePicker';
import { getArticleById } from '../../../services/articleService';
import { getMagasins, getFournisseurs, getClients, getCodesMouvement, getStocksSpeciaux } from '../../../services/referentielService';

const ArticleDocuments = () => {
  const { id } = useParams();
  
  const [article, setArticle] = useState(null);
  const [magasins, setMagasins] = useState([]);
  const [fournisseurs, setFournisseurs] = useState([]);
  const [clients, setClients] = useState([]);
  const [codesMouvement, setCodesMouvement] = useState([]);
  const [stocksSpeciaux, setStocksSpeciaux] = useState([]);
  
  const [formData, setFormData] = useState({
    article: id,
    division: '',
    magasin: '',
    lot: '',
    fournisseur: '',
    client: '',
    codeMouvement: '',
    stockSpecial: '',
    dateComptable: '',
    dateSaisie: '',
    heureSaisie: '00:00:00',
    nomUtilisateur: '',
    typeOperation: '',
    docArticle: '',
    exerciceDoc: '',
    codeTransaction: '',
    reference: '',
    tri: 'numero',
    ordre: 'asc',
    maxLignes: 100
  });
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Charger toutes les données nécessaires en parallèle
        const [articleData, magasinsData, fournisseursData, clientsData, codesMouvementData, stocksSpeciauxData] = await Promise.all([
          getArticleById(id),
          getMagasins(),
          getFournisseurs(),
          getClients(),
          getCodesMouvement(),
          getStocksSpeciaux()
        ]);
        
        setArticle(articleData);
        setMagasins(magasinsData);
        setFournisseurs(fournisseursData);
        setClients(clientsData);
        setCodesMouvement(codesMouvementData);
        setStocksSpeciaux(stocksSpeciauxData);
        
        // Mettre à jour le formulaire avec les données de l'article
        setFormData(prev => ({
          ...prev,
          division: articleData.division
        }));
        
        setLoading(false);
      } catch (err) {
        setError('Erreur lors du chargement des données');
        setLoading(false);
        console.error(err);
      }
    };
    
    fetchData();
  }, [id]);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleDateChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Données à envoyer:', formData);
    // Ici, vous appelleriez votre API pour enregistrer les données
    alert('Données envoyées avec succès!');
  };
  
  if (loading) return <div>Chargement des données...</div>;
  if (error) return <div>Erreur: {error}</div>;
  
  return (
    <SapContainer title="Liste des documents article">
      <form onSubmit={handleSubmit}>
        <SapBlock title="Données de poste">
          <SapFieldRow>
            <SapField label="Article">
              <select 
                className="sap-select" 
                name="article" 
                value={formData.article} 
                onChange={handleChange}
                disabled
              >
                <option value={article.id}>{article.id} - {article.designation}</option>
              </select>
            </SapField>
            
            <SapField label="Division">
              <input 
                type="text" 
                className="sap-input" 
                name="division" 
                value={formData.division} 
                onChange={handleChange}
                readOnly
              />
            </SapField>
          </SapFieldRow>
          
          <SapFieldRow>
            <SapField label="Magasin">
              <select 
                className="sap-select" 
                name="magasin" 
                value={formData.magasin} 
                onChange={handleChange}
              >
                <option value="">Sélectionnez un magasin</option>
                {magasins.map(magasin => (
                  <option key={magasin.code} value={magasin.code}>
                    {magasin.code} - {magasin.nom}
                  </option>
                ))}
              </select>
            </SapField>
            
            <SapField label="Lot">
              <input 
                type="text" 
                className="sap-input" 
                name="lot" 
                value={formData.lot} 
                onChange={handleChange}
              />
            </SapField>
          </SapFieldRow>
          
          <SapFieldRow>
            <SapField label="Fournisseur">
              <select 
                className="sap-select" 
                name="fournisseur" 
                value={formData.fournisseur} 
                onChange={handleChange}
              >
                <option value="">Sélectionnez un fournisseur</option>
                {fournisseurs.map(fournisseur => (
                  <option key={fournisseur.code} value={fournisseur.code}>
                    {fournisseur.code} - {fournisseur.nom}
                  </option>
                ))}
              </select>
            </SapField>
            
            <SapField label="Client">
              <select 
                className="sap-select" 
                name="client" 
                value={formData.client} 
                onChange={handleChange}
              >
                <option value="">Sélectionnez un client</option>
                {clients.map(client => (
                  <option key={client.code} value={client.code}>
                    {client.code} - {client.nom}
                  </option>
                ))}
              </select>
            </SapField>
          </SapFieldRow>
          
          <SapFieldRow>
            <SapField label="Code mouvement">
              <select 
                className="sap-select" 
                name="codeMouvement" 
                value={formData.codeMouvement} 
                onChange={handleChange}
              >
                <option value="">Sélectionnez un code</option>
                {codesMouvement.map(code => (
                  <option key={code.code} value={code.code}>
                    {code.code} - {code.libelle}
                  </option>
                ))}
              </select>
            </SapField>
            
            <SapField label="Stock spécial">
              <select 
                className="sap-select" 
                name="stockSpecial" 
                value={formData.stockSpecial} 
                onChange={handleChange}
              >
                <option value="">Sélectionnez un stock</option>
                {stocksSpeciaux.map(stock => (
                  <option key={stock.code} value={stock.code}>
                    {stock.code} - {stock.libelle}
                  </option>
                ))}
              </select>
            </SapField>
          </SapFieldRow>
        </SapBlock>
        
        <SapBlock title="Données d'en-tête">
          <SapFieldRow>
            <SapField label="Date comptable">
              <SapDatePicker 
                id="date-comptable"
                value={formData.dateComptable}
                onChange={(value) => handleDateChange('dateComptable', value)}
              />
            </SapField>
            
            <SapField label="Nom d'utilisateur">
              <input 
                type="text" 
                className="sap-input" 
                name="nomUtilisateur" 
                value={formData.nomUtilisateur} 
                onChange={handleChange}
              />
            </SapField>
          </SapFieldRow>
          
          <SapFieldRow>
            <SapField label="Type d'opération">
              <input 
                type="text" 
                className="sap-input" 
                name="typeOperation" 
                value={formData.typeOperation} 
                onChange={handleChange}
              />
            </SapField>
            
            <SapField label="Date de saisie">
              <SapDatePicker 
                id="date-saisie"
                value={formData.dateSaisie}
                onChange={(value) => handleDateChange('dateSaisie', value)}
              />
            </SapField>
          </SapFieldRow>
          
          <SapFieldRow>
            <SapField label="Heure de saisie">
              <input 
                type="text" 
                className="sap-input" 
                name="heureSaisie" 
                value={formData.heureSaisie} 
                onChange={handleChange}
                placeholder="HH:MM:SS"
              />
            </SapField>
            
            <SapField label="Document article">
              <input 
                type="text" 
                className="sap-input" 
                name="docArticle" 
                value={formData.docArticle} 
                onChange={handleChange}
              />
            </SapField>
          </SapFieldRow>
          
          <SapFieldRow>
            <SapField label="Exercice doc.article">
              <input 
                type="text" 
                className="sap-input" 
                name="exerciceDoc" 
                value={formData.exerciceDoc} 
                onChange={handleChange}
              />
            </SapField>
            
            <SapField label="Code de transaction">
              <input 
                type="text" 
                className="sap-input" 
                name="codeTransaction" 
                value={formData.codeTransaction} 
                onChange={handleChange}
              />
            </SapField>
          </SapFieldRow>
          
          <SapFieldRow>
            <SapField label="Référence">
              <input 
                type="text" 
                className="sap-input" 
                name="reference" 
                value={formData.reference} 
                onChange={handleChange}
              />
            </SapField>
          </SapFieldRow>
        </SapBlock>
        
        <SapBlock title="Options d'affichage">
          <SapFieldRow>
            <SapField label="Tri par">
              <select 
                className="sap-select" 
                name="tri" 
                value={formData.tri} 
                onChange={handleChange}
              >
                <option value="numero">Numéro document</option>
                <option value="date">Date</option>
                <option value="article">Article</option>
              </select>
            </SapField>
            
            <SapField label="Ordre">
              <select 
                className="sap-select" 
                name="ordre" 
                value={formData.ordre} 
                onChange={handleChange}
              >
                <option value="asc">Ascendant</option>
                <option value="desc">Descendant</option>
              </select>
            </SapField>
          </SapFieldRow>
          
          <SapFieldRow>
            <SapField label="Nombre max. de lignes">
              <input 
                type="number" 
                className="sap-input" 
                name="maxLignes" 
                value={formData.maxLignes} 
                onChange={handleChange}
                style={{ width: '80px' }}
              />
            </SapField>
          </SapFieldRow>
        </SapBlock>
        
        <div className="sap-buttons">
          <SapButton type="help" onClick={() => alert('Aide : Sélectionnez les éléments dans les listes déroulantes.\nLes champs sont automatiquement mis à jour en fonction de vos sélections.')}>
            Aide
          </SapButton>
          <SapButton onClick={() => window.location.reload()}>
            Annuler
          </SapButton>
          <SapButton type="primary" onClick={handleSubmit}>
            Afficher
          </SapButton>
        </div>
      </form>
    </SapContainer>
  );
};

export default ArticleDocuments;
