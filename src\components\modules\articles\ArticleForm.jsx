import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import SapContainer from '../../common/SapContainer';
import SapBlock from '../../common/SapBlock';
import SapField from '../../common/SapField';
import SapFieldRow from '../../common/SapFieldRow';
import SapButton from '../../common/SapButton';
import { getArticleById, saveArticle } from '../../../services/articleService';

const ArticleForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isNewArticle = id === 'new';
  
  const [article, setArticle] = useState({
    id: '',
    designation: '',
    division: '',
    prix: 0,
    description: '',
    unite: '',
    stockMin: 0,
    stockMax: 0,
    fournisseurPrincipal: ''
  });
  
  const [loading, setLoading] = useState(!isNewArticle);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const fetchArticle = async () => {
      if (isNewArticle) return;
      
      try {
        setLoading(true);
        const data = await getArticleById(id);
        setArticle(data);
        setLoading(false);
      } catch (err) {
        setError('Erreur lors du chargement de l\'article');
        setLoading(false);
        console.error(err);
      }
    };
    
    fetchArticle();
  }, [id, isNewArticle]);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setArticle(prev => ({
      ...prev,
      [name]: name === 'prix' || name === 'stockMin' || name === 'stockMax' 
        ? parseFloat(value) || 0 
        : value
    }));
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      await saveArticle(article);
      navigate('/articles');
    } catch (err) {
      setError('Erreur lors de l\'enregistrement de l\'article');
      console.error(err);
    }
  };
  
  if (loading) return <div>Chargement de l'article...</div>;
  if (error) return <div>Erreur: {error}</div>;
  
  return (
    <SapContainer title={isNewArticle ? "Nouvel Article" : "Modifier Article"}>
      <form onSubmit={handleSubmit}>
        <SapBlock title="Informations Générales">
          <SapFieldRow>
            <SapField label="Code Article">
              <input 
                type="text" 
                className="sap-input" 
                name="id" 
                value={article.id} 
                onChange={handleChange}
                disabled={!isNewArticle}
                required
              />
            </SapField>
            
            <SapField label="Désignation">
              <input 
                type="text" 
                className="sap-input" 
                name="designation" 
                value={article.designation} 
                onChange={handleChange}
                required
              />
            </SapField>
          </SapFieldRow>
          
          <SapFieldRow>
            <SapField label="Division">
              <input 
                type="text" 
                className="sap-input" 
                name="division" 
                value={article.division} 
                onChange={handleChange}
                required
              />
            </SapField>
            
            <SapField label="Prix">
              <input 
                type="number" 
                step="0.01" 
                className="sap-input" 
                name="prix" 
                value={article.prix} 
                onChange={handleChange}
                required
              />
            </SapField>
          </SapFieldRow>
          
          <SapFieldRow>
            <SapField label="Unité">
              <input 
                type="text" 
                className="sap-input" 
                name="unite" 
                value={article.unite} 
                onChange={handleChange}
              />
            </SapField>
            
            <SapField label="Fournisseur">
              <input 
                type="text" 
                className="sap-input" 
                name="fournisseurPrincipal" 
                value={article.fournisseurPrincipal} 
                onChange={handleChange}
              />
            </SapField>
          </SapFieldRow>
        </SapBlock>
        
        <SapBlock title="Gestion des Stocks">
          <SapFieldRow>
            <SapField label="Stock Minimum">
              <input 
                type="number" 
                className="sap-input" 
                name="stockMin" 
                value={article.stockMin} 
                onChange={handleChange}
              />
            </SapField>
            
            <SapField label="Stock Maximum">
              <input 
                type="number" 
                className="sap-input" 
                name="stockMax" 
                value={article.stockMax} 
                onChange={handleChange}
              />
            </SapField>
          </SapFieldRow>
        </SapBlock>
        
        <SapBlock title="Description">
          <SapFieldRow>
            <SapField label="Description">
              <textarea 
                className="sap-input" 
                name="description" 
                value={article.description} 
                onChange={handleChange}
                rows="4"
              />
            </SapField>
          </SapFieldRow>
        </SapBlock>
        
        <div className="sap-buttons">
          <SapButton onClick={() => navigate('/articles')}>Annuler</SapButton>
          <SapButton type="primary" onClick={handleSubmit}>Enregistrer</SapButton>
        </div>
      </form>
    </SapContainer>
  );
};

export default ArticleForm;
