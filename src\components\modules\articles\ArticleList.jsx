import React, { useState, useEffect } from 'react';
import SapContainer from '../../common/SapContainer';
import SapBlock from '../../common/SapBlock';
import SapButton from '../../common/SapButton';
import { getArticles } from '../../../services/articleService';

const ArticleList = () => {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchArticles = async () => {
      try {
        setLoading(true);
        const data = await getArticles();
        setArticles(data);
        setLoading(false);
      } catch (err) {
        setError('Erreur lors du chargement des articles');
        setLoading(false);
        console.error(err);
      }
    };

    fetchArticles();
  }, []);

  if (loading) return <div>Chargement des articles...</div>;
  if (error) return <div>Erreur: {error}</div>;

  return (
    <SapContainer title="Gestion des Articles">
      <SapBlock title="Liste des Articles">
        <div className="sap-table-container">
          <table className="sap-table">
            <thead>
              <tr>
                <th>Code</th>
                <th>Désignation</th>
                <th>Division</th>
                <th>Prix</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {articles.map(article => (
                <tr key={article.id}>
                  <td>{article.id}</td>
                  <td>{article.designation}</td>
                  <td>{article.division}</td>
                  <td>{article.prix.toFixed(2)} €</td>
                  <td>
                    <SapButton type="primary" onClick={() => window.location.href = `/articles/${article.id}`}>
                      Détails
                    </SapButton>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </SapBlock>
      <div className="sap-buttons">
        <SapButton type="primary" onClick={() => window.location.href = '/articles/new'}>
          Nouvel Article
        </SapButton>
      </div>
    </SapContainer>
  );
};

export default ArticleList;
