// Données de test (à remplacer par des appels API réels)
const mockArticles = [
  { id: "6015379", designation: "Produit A", division: "9505", prix: 125.50, description: "Description du produit A", unite: "PCE", stockMin: 10, stockMax: 100, fournisseurPrincipal: "F001" },
  { id: "6015380", designation: "Produit B", division: "9505", prix: 89.99, description: "Description du produit B", unite: "KG", stockMin: 5, stockMax: 50, fournisseurPrincipal: "F002" },
  { id: "6015381", designation: "Produit C", division: "9602", prix: 45.20, description: "Description du produit C", unite: "L", stockMin: 20, stockMax: 200, fournisseurPrincipal: "F001" },
  { id: "6015382", designation: "Produit D", division: "9602", prix: 210.00, description: "Description du produit D", unite: "PCE", stockMin: 2, stockMax: 30, fournisseurPrincipal: "F003" }
];

// Fonction pour simuler un délai réseau
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Récupère la liste de tous les articles
 * @returns {Promise<Array>} Liste des articles
 */
export const getArticles = async () => {
  // Simuler un délai réseau
  await delay(500);
  return [...mockArticles];
};

/**
 * Récupère un article par son ID
 * @param {string} id - ID de l'article à récupérer
 * @returns {Promise<Object>} Données de l'article
 */
export const getArticleById = async (id) => {
  // Simuler un délai réseau
  await delay(300);
  
  const article = mockArticles.find(a => a.id === id);
  
  if (!article) {
    throw new Error(`Article avec l'ID ${id} non trouvé`);
  }
  
  return { ...article };
};

/**
 * Enregistre un article (création ou mise à jour)
 * @param {Object} article - Données de l'article à enregistrer
 * @returns {Promise<Object>} Article enregistré
 */
export const saveArticle = async (article) => {
  // Simuler un délai réseau
  await delay(700);
  
  // Vérifier si l'article existe déjà
  const index = mockArticles.findIndex(a => a.id === article.id);
  
  if (index >= 0) {
    // Mise à jour
    mockArticles[index] = { ...article };
    return { ...article };
  } else {
    // Création
    mockArticles.push({ ...article });
    return { ...article };
  }
};

/**
 * Supprime un article par son ID
 * @param {string} id - ID de l'article à supprimer
 * @returns {Promise<boolean>} true si supprimé avec succès
 */
export const deleteArticle = async (id) => {
  // Simuler un délai réseau
  await delay(500);
  
  const index = mockArticles.findIndex(a => a.id === id);
  
  if (index >= 0) {
    mockArticles.splice(index, 1);
    return true;
  } else {
    throw new Error(`Article avec l'ID ${id} non trouvé`);
  }
};
