import { supabase } from './supabaseClient';

/**
 * Récupère la liste de tous les articles
 * @returns {Promise<Array>} Liste des articles
 */
export const getArticles = async () => {
  const { data, error } = await supabase
    .from('articles')
    .select('*');
  
  if (error) {
    console.error('Erreur lors de la récupération des articles:', error);
    throw error;
  }
  
  return data;
};

/**
 * Récupère un article par son ID
 * @param {string} id - ID de l'article à récupérer
 * @returns {Promise<Object>} Données de l'article
 */
export const getArticleById = async (id) => {
  const { data, error } = await supabase
    .from('articles')
    .select('*')
    .eq('id', id)
    .single();
  
  if (error) {
    console.error(`Erreur lors de la récupération de l'article ${id}:`, error);
    throw error;
  }
  
  return data;
};

/**
 * Enregistre un article (création ou mise à jour)
 * @param {Object} article - Données de l'article à enregistrer
 * @returns {Promise<Object>} Article enregistré
 */
export const saveArticle = async (article) => {
  // Vérifier si l'article existe déjà
  const { data: existingArticle } = await supabase
    .from('articles')
    .select('id')
    .eq('id', article.id)
    .maybeSingle();
  
  let result;
  
  if (existingArticle) {
    // Mise à jour
    const { data, error } = await supabase
      .from('articles')
      .update(article)
      .eq('id', article.id)
      .select()
      .single();
    
    if (error) {
      console.error(`Erreur lors de la mise à jour de l'article ${article.id}:`, error);
      throw error;
    }
    
    result = data;
  } else {
    // Création
    const { data, error } = await supabase
      .from('articles')
      .insert(article)
      .select()
      .single();
    
    if (error) {
      console.error('Erreur lors de la création de l\'article:', error);
      throw error;
    }
    
    result = data;
  }
  
  return result;
};

/**
 * Supprime un article par son ID
 * @param {string} id - ID de l'article à supprimer
 * @returns {Promise<boolean>} true si supprimé avec succès
 */
export const deleteArticle = async (id) => {
  const { error } = await supabase
    .from('articles')
    .delete()
    .eq('id', id);
  
  if (error) {
    console.error(`Erreur lors de la suppression de l'article ${id}:`, error);
    throw error;
  }
  
  return true;
};
