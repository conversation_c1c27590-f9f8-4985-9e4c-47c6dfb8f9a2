// Données de test (à remplacer par des appels API réels)
const mockData = {
  magasins: [
    { code: "M001", nom: "<PERSON><PERSON><PERSON> Principal", ville: "Paris" },
    { code: "M002", nom: "Magasin Est", ville: "Lyon" },
    { code: "M003", nom: "Magasin Ouest", ville: "Nantes" }
  ],
  fournisseurs: [
    { code: "F001", nom: "Fournisseur Alpha", contact: "<PERSON><PERSON>" },
    { code: "F002", nom: "Fournisseur Beta", contact: "<PERSON><PERSON> Martin" },
    { code: "F003", nom: "Fournisseur Gamma", contact: "<PERSON><PERSON>" }
  ],
  clients: [
    { code: "C001", nom: "Client Premium", ville: "Marseille" },
    { code: "C002", nom: "Client Standard", ville: "Toulouse" },
    { code: "C003", nom: "Client VIP", ville: "Lille" }
  ],
  codesMouvement: [
    { code: "101", libelle: "Entrée stock", type: "entree" },
    { code: "102", libelle: "Sortie stock", type: "sortie" },
    { code: "103", libelle: "Transfert", type: "transfert" },
    { code: "104", libelle: "Inventaire", type: "inventaire" }
  ],
  stocksSpeciaux: [
    { code: "S1", libelle: "Stock qualité" },
    { code: "S2", libelle: "Stock bloqué" },
    { code: "S3", libelle: "Stock retour" }
  ]
};

// Fonction pour simuler un délai réseau
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Récupère la liste des magasins
 * @returns {Promise<Array>} Liste des magasins
 */
export const getMagasins = async () => {
  await delay(300);
  return [...mockData.magasins];
};

/**
 * Récupère la liste des fournisseurs
 * @returns {Promise<Array>} Liste des fournisseurs
 */
export const getFournisseurs = async () => {
  await delay(300);
  return [...mockData.fournisseurs];
};

/**
 * Récupère la liste des clients
 * @returns {Promise<Array>} Liste des clients
 */
export const getClients = async () => {
  await delay(300);
  return [...mockData.clients];
};

/**
 * Récupère la liste des codes de mouvement
 * @returns {Promise<Array>} Liste des codes de mouvement
 */
export const getCodesMouvement = async () => {
  await delay(300);
  return [...mockData.codesMouvement];
};

/**
 * Récupère la liste des stocks spéciaux
 * @returns {Promise<Array>} Liste des stocks spéciaux
 */
export const getStocksSpeciaux = async () => {
  await delay(300);
  return [...mockData.stocksSpeciaux];
};
