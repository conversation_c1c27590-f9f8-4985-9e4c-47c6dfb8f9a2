import { supabase } from './supabaseClient';

/**
 * Récupère la liste des magasins
 * @returns {Promise<Array>} Liste des magasins
 */
export const getMagasins = async () => {
  const { data, error } = await supabase
    .from('magasins')
    .select('*');
  
  if (error) {
    console.error('Erreur lors de la récupération des magasins:', error);
    throw error;
  }
  
  return data;
};

/**
 * Récupère la liste des fournisseurs
 * @returns {Promise<Array>} Liste des fournisseurs
 */
export const getFournisseurs = async () => {
  const { data, error } = await supabase
    .from('fournisseurs')
    .select('*');
  
  if (error) {
    console.error('Erreur lors de la récupération des fournisseurs:', error);
    throw error;
  }
  
  return data;
};

/**
 * Récupère la liste des clients
 * @returns {Promise<Array>} Liste des clients
 */
export const getClients = async () => {
  const { data, error } = await supabase
    .from('clients')
    .select('*');
  
  if (error) {
    console.error('Erreur lors de la récupération des clients:', error);
    throw error;
  }
  
  return data;
};

/**
 * Récupère la liste des codes de mouvement
 * @returns {Promise<Array>} Liste des codes de mouvement
 */
export const getCodesMouvement = async () => {
  const { data, error } = await supabase
    .from('codes_mouvement')
    .select('*');
  
  if (error) {
    console.error('Erreur lors de la récupération des codes de mouvement:', error);
    throw error;
  }
  
  return data;
};

/**
 * Récupère la liste des stocks spéciaux
 * @returns {Promise<Array>} Liste des stocks spéciaux
 */
export const getStocksSpeciaux = async () => {
  const { data, error } = await supabase
    .from('stocks_speciaux')
    .select('*');
  
  if (error) {
    console.error('Erreur lors de la récupération des stocks spéciaux:', error);
    throw error;
  }
  
  return data;
};
