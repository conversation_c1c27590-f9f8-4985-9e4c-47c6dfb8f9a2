:root {
    --sap-blue: #366092;
    --sap-light-blue: #5b9bd5;
    --sap-gray: #a5a5a5;
    --sap-dark-gray: #333;
    --sap-bg: #f0f0f0;
    --sap-border: #d1d1d1;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: var(--sap-bg);
    margin: 0;
    padding: 20px;
    color: var(--sap-dark-gray);
    font-size: 14px;
}

.sap-container {
    max-width: 1000px;
    margin: 0 auto;
    box-shadow: 0 0 15px rgba(0,0,0,0.1);
}

.sap-header {
    background-color: var(--sap-blue);
    color: white;
    padding: 8px 15px;
    font-size: 1.1em;
    font-weight: bold;
}

.sap-block {
    background-color: white;
    border: 1px solid var(--sap-border);
    margin-bottom: 15px;
}

.sap-block-title {
    background-color: #f5f5f5;
    color: var(--sap-dark-gray);
    padding: 6px 15px;
    font-weight: bold;
    border-bottom: 1px solid var(--sap-border);
    font-size: 1em;
}

.sap-form {
    padding: 10px 15px;
}

.sap-field-row {
    display: flex;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.sap-field {
    flex: 1;
    min-width: 250px;
    margin-right: 15px;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
}

.sap-field:last-child {
    margin-right: 0;
}

.sap-label {
    display: inline-block;
    width: 120px;
    font-weight: normal;
}

.sap-input {
    flex: 1;
    padding: 5px 8px;
    border: 1px solid var(--sap-border);
    background-color: white;
}

.sap-select {
    flex: 1;
    padding: 5px 8px;
    border: 1px solid var(--sap-border);
    background-color: white;
}

.sap-date-container {
    position: relative;
    flex: 1;
    display: flex;
}

.sap-date-input {
    padding: 5px 8px;
    border: 1px solid var(--sap-border);
    flex: 1;
}

.sap-date-trigger {
    background: #f5f5f5;
    border: 1px solid var(--sap-border);
    border-left: none;
    padding: 5px 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.sap-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-top: 1px solid var(--sap-border);
}

.sap-button {
    padding: 6px 12px;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-weight: bold;
    cursor: pointer;
    min-width: 80px;
    font-size: 13px;
    background-color: #f9f9f9;
}

.sap-button-primary {
    background-color: var(--sap-light-blue);
    color: white;
    border-color: var(--sap-light-blue);
}

.sap-button-help {
    background-color: #e6f0fa;
    color: var(--sap-blue);
    border-color: var(--sap-blue);
}

@media (max-width: 600px) {
    .sap-field {
        min-width: 100%;
        margin-right: 0;
    }
}
