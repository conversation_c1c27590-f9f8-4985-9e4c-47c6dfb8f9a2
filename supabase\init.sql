-- Création des tables

-- Table articles
CREATE TABLE articles (
    id TEXT PRIMARY KEY,
    designation TEXT NOT NULL,
    division TEXT NOT NULL,
    prix NUMERIC(10, 2) NOT NULL,
    description TEXT,
    unite TEXT,
    stockMin INTEGER DEFAULT 0,
    stockMax INTEGER DEFAULT 0,
    fournisseurPrincipal TEXT
);

-- Table magasins
CREATE TABLE magasins (
    code TEXT PRIMARY KEY,
    nom TEXT NOT NULL,
    ville TEXT
);

-- Table fournisseurs
CREATE TABLE fournisseurs (
    code TEXT PRIMARY KEY,
    nom TEXT NOT NULL,
    contact TEXT
);

-- Table clients
CREATE TABLE clients (
    code TEXT PRIMARY KEY,
    nom TEXT NOT NULL,
    ville TEXT
);

-- Table codes_mouvement
CREATE TABLE codes_mouvement (
    code TEXT PRIMARY KEY,
    libelle TEXT NOT NULL,
    type TEXT NOT NULL
);

-- Table stocks_speciaux
CREATE TABLE stocks_speciaux (
    code TEXT PRIMARY KEY,
    libelle TEXT NOT NULL
);

-- Table documents_article
CREATE TABLE documents_article (
    id SERIAL PRIMARY KEY,
    article_id TEXT NOT NULL REFERENCES articles(id),
    magasin_code TEXT REFERENCES magasins(code),
    lot TEXT,
    fournisseur_code TEXT REFERENCES fournisseurs(code),
    client_code TEXT REFERENCES clients(code),
    code_mouvement TEXT REFERENCES codes_mouvement(code),
    stock_special TEXT REFERENCES stocks_speciaux(code),
    date_comptable DATE,
    date_saisie DATE,
    heure_saisie TIME,
    nom_utilisateur TEXT,
    type_operation TEXT,
    doc_article TEXT,
    exercice_doc TEXT,
    code_transaction TEXT,
    reference TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insertion des données de test

-- Articles
INSERT INTO articles (id, designation, division, prix, description, unite, stockMin, stockMax, fournisseurPrincipal)
VALUES 
    ('6015379', 'Produit A', '9505', 125.50, 'Description du produit A', 'PCE', 10, 100, 'F001'),
    ('6015380', 'Produit B', '9505', 89.99, 'Description du produit B', 'KG', 5, 50, 'F002'),
    ('6015381', 'Produit C', '9602', 45.20, 'Description du produit C', 'L', 20, 200, 'F001'),
    ('6015382', 'Produit D', '9602', 210.00, 'Description du produit D', 'PCE', 2, 30, 'F003');

-- Magasins
INSERT INTO magasins (code, nom, ville)
VALUES 
    ('M001', 'Magasin Principal', 'Paris'),
    ('M002', 'Magasin Est', 'Lyon'),
    ('M003', 'Magasin Ouest', 'Nantes');

-- Fournisseurs
INSERT INTO fournisseurs (code, nom, contact)
VALUES 
    ('F001', 'Fournisseur Alpha', 'M. Dupont'),
    ('F002', 'Fournisseur Beta', 'Mme Martin'),
    ('F003', 'Fournisseur Gamma', 'M. Leroy');

-- Clients
INSERT INTO clients (code, nom, ville)
VALUES 
    ('C001', 'Client Premium', 'Marseille'),
    ('C002', 'Client Standard', 'Toulouse'),
    ('C003', 'Client VIP', 'Lille');

-- Codes mouvement
INSERT INTO codes_mouvement (code, libelle, type)
VALUES 
    ('101', 'Entrée stock', 'entree'),
    ('102', 'Sortie stock', 'sortie'),
    ('103', 'Transfert', 'transfert'),
    ('104', 'Inventaire', 'inventaire');

-- Stocks spéciaux
INSERT INTO stocks_speciaux (code, libelle)
VALUES 
    ('S1', 'Stock qualité'),
    ('S2', 'Stock bloqué'),
    ('S3', 'Stock retour');

-- Création des politiques de sécurité Row Level Security (RLS)
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE magasins ENABLE ROW LEVEL SECURITY;
ALTER TABLE fournisseurs ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE codes_mouvement ENABLE ROW LEVEL SECURITY;
ALTER TABLE stocks_speciaux ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents_article ENABLE ROW LEVEL SECURITY;

-- Politique par défaut: les utilisateurs authentifiés peuvent tout faire
CREATE POLICY "Utilisateurs authentifiés peuvent tout faire" ON articles FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Utilisateurs authentifiés peuvent tout faire" ON magasins FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Utilisateurs authentifiés peuvent tout faire" ON fournisseurs FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Utilisateurs authentifiés peuvent tout faire" ON clients FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Utilisateurs authentifiés peuvent tout faire" ON codes_mouvement FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Utilisateurs authentifiés peuvent tout faire" ON stocks_speciaux FOR ALL USING (auth.role() = 'authenticated');
CREATE POLICY "Utilisateurs authentifiés peuvent tout faire" ON documents_article FOR ALL USING (auth.role() = 'authenticated');
