import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/layout/Layout';
import ArticleList from './components/articles/ArticleList';
import ArticleForm from './components/articles/ArticleForm';
import ArticleDocuments from './components/articles/ArticleDocuments';
import './styles/index.css';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Navigate to="/articles" replace />} />
          <Route path="articles" element={<ArticleList />} />
          <Route path="articles/:id" element={<ArticleForm />} />
          <Route path="articles/:id/documents" element={<ArticleDocuments />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;
