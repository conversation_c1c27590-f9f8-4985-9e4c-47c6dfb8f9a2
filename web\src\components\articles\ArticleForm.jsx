import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { articleService } from '../../services/api';
import './Articles.css';

const ArticleForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isNewArticle = id === 'new';
  
  const [article, setArticle] = useState({
    id: '',
    designation: '',
    division: '',
    prix: 0,
    description: '',
    unite: '',
    stockMin: 0,
    stockMax: 0,
    fournisseurPrincipal: ''
  });
  
  const [loading, setLoading] = useState(!isNewArticle);
  const [error, setError] = useState(null);
  const [saving, setSaving] = useState(false);
  
  useEffect(() => {
    const fetchArticle = async () => {
      if (isNewArticle) return;
      
      try {
        setLoading(true);
        const data = await articleService.getById(id);
        setArticle(data);
        setLoading(false);
      } catch (err) {
        setError('Erreur lors du chargement de l\'article');
        setLoading(false);
        console.error(err);
      }
    };
    
    fetchArticle();
  }, [id, isNewArticle]);
  
  const handleChange = (e) => {
    const { name, value, type } = e.target;
    setArticle(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      if (isNewArticle) {
        await articleService.create(article);
      } else {
        await articleService.update(id, article);
      }
      
      setSaving(false);
      navigate('/articles');
    } catch (err) {
      setError('Erreur lors de l\'enregistrement de l\'article');
      setSaving(false);
      console.error(err);
    }
  };
  
  if (loading) return <div className="loading">Chargement de l'article...</div>;
  
  return (
    <div className="article-form">
      <div className="card">
        <div className="card-header">
          {isNewArticle ? 'Nouvel Article' : 'Modifier Article'}
        </div>
        <div className="card-body">
          {error && <div className="error mb-2">{error}</div>}
          
          <form onSubmit={handleSubmit}>
            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label className="form-label">Code Article</label>
                  <input 
                    type="text" 
                    className="form-control" 
                    name="id" 
                    value={article.id} 
                    onChange={handleChange}
                    disabled={!isNewArticle}
                    required
                  />
                </div>
              </div>
              
              <div className="form-col">
                <div className="form-group">
                  <label className="form-label">Désignation</label>
                  <input 
                    type="text" 
                    className="form-control" 
                    name="designation" 
                    value={article.designation} 
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
            </div>
            
            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label className="form-label">Division</label>
                  <input 
                    type="text" 
                    className="form-control" 
                    name="division" 
                    value={article.division} 
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
              
              <div className="form-col">
                <div className="form-group">
                  <label className="form-label">Prix</label>
                  <input 
                    type="number" 
                    step="0.01" 
                    className="form-control" 
                    name="prix" 
                    value={article.prix} 
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
            </div>
            
            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label className="form-label">Unité</label>
                  <input 
                    type="text" 
                    className="form-control" 
                    name="unite" 
                    value={article.unite} 
                    onChange={handleChange}
                  />
                </div>
              </div>
              
              <div className="form-col">
                <div className="form-group">
                  <label className="form-label">Fournisseur Principal</label>
                  <input 
                    type="text" 
                    className="form-control" 
                    name="fournisseurPrincipal" 
                    value={article.fournisseurPrincipal} 
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>
            
            <div className="form-row">
              <div className="form-col">
                <div className="form-group">
                  <label className="form-label">Stock Minimum</label>
                  <input 
                    type="number" 
                    className="form-control" 
                    name="stockMin" 
                    value={article.stockMin} 
                    onChange={handleChange}
                  />
                </div>
              </div>
              
              <div className="form-col">
                <div className="form-group">
                  <label className="form-label">Stock Maximum</label>
                  <input 
                    type="number" 
                    className="form-control" 
                    name="stockMax" 
                    value={article.stockMax} 
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>
            
            <div className="form-group">
              <label className="form-label">Description</label>
              <textarea 
                className="form-control" 
                name="description" 
                value={article.description} 
                onChange={handleChange}
                rows="4"
              />
            </div>
            
            <div className="flex justify-between mt-2">
              <Link to="/articles" className="btn btn-secondary">Annuler</Link>
              <button 
                type="submit" 
                className="btn" 
                disabled={saving}
              >
                {saving ? 'Enregistrement...' : 'Enregistrer'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ArticleForm;
