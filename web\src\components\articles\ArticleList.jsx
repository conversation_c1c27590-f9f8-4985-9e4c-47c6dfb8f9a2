import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { articleService } from '../../services/api';
import './Articles.css';

const ArticleList = () => {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchArticles = async () => {
      try {
        setLoading(true);
        const data = await articleService.getAll();
        setArticles(data);
        setLoading(false);
      } catch (err) {
        setError('Erreur lors du chargement des articles');
        setLoading(false);
        console.error(err);
      }
    };

    fetchArticles();
  }, []);

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet article ?')) {
      try {
        await articleService.delete(id);
        setArticles(articles.filter(article => article.id !== id));
      } catch (err) {
        setError('Erreur lors de la suppression de l\'article');
        console.error(err);
      }
    }
  };

  if (loading) return <div className="loading">Chargement des articles...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="article-list">
      <div className="card">
        <div className="card-header">Liste des Articles</div>
        <div className="card-body">
          <div className="actions mb-2">
            <Link to="/articles/new" className="btn">Nouvel Article</Link>
          </div>
          
          <table className="table">
            <thead>
              <tr>
                <th>Code</th>
                <th>Désignation</th>
                <th>Division</th>
                <th>Prix</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {articles.length === 0 ? (
                <tr>
                  <td colSpan="5" className="text-center">Aucun article trouvé</td>
                </tr>
              ) : (
                articles.map(article => (
                  <tr key={article.id}>
                    <td>{article.id}</td>
                    <td>{article.designation}</td>
                    <td>{article.division}</td>
                    <td>{parseFloat(article.prix).toFixed(2)} €</td>
                    <td>
                      <div className="flex gap-1">
                        <Link to={`/articles/${article.id}`} className="btn btn-sm">Modifier</Link>
                        <Link to={`/articles/${article.id}/documents`} className="btn btn-sm btn-secondary">Documents</Link>
                        <button 
                          className="btn btn-sm btn-danger"
                          onClick={() => handleDelete(article.id)}
                        >
                          Supprimer
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ArticleList;
