import React from 'react';
import { Outlet, Link } from 'react-router-dom';
import './Layout.css';

const Layout = () => {
  return (
    <div className="layout">
      <header className="header">
        <div className="logo">ERP PME</div>
        <nav className="nav">
          <ul>
            <li><Link to="/articles">Articles</Link></li>
            <li><Link to="/clients">Clients</Link></li>
            <li><Link to="/fournisseurs">Fournisseurs</Link></li>
            <li><Link to="/stock">Stock</Link></li>
          </ul>
        </nav>
        <div className="user">
          <span className="user-name">Admin</span>
          <button className="logout-btn">Déconnexion</button>
        </div>
      </header>
      
      <main className="main">
        <div className="container">
          <Outlet />
        </div>
      </main>
      
      <footer className="footer">
        <div className="container">
          <p>&copy; 2023 ERP PME - Tous droits réservés</p>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
