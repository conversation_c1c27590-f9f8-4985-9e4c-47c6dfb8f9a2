import axios from 'axios';

// Création d'une instance axios avec l'URL de base de l'API
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Services pour les articles
export const articleService = {
  // Récupérer tous les articles
  getAll: async () => {
    try {
      const response = await api.get('/articles');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des articles:', error);
      throw error;
    }
  },
  
  // Récupérer un article par son ID
  getById: async (id) => {
    try {
      const response = await api.get(`/articles/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'article ${id}:`, error);
      throw error;
    }
  },
  
  // Créer un nouvel article
  create: async (article) => {
    try {
      const response = await api.post('/articles', article);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création de l\'article:', error);
      throw error;
    }
  },
  
  // Mettre à jour un article
  update: async (id, article) => {
    try {
      const response = await api.put(`/articles/${id}`, article);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de l'article ${id}:`, error);
      throw error;
    }
  },
  
  // Supprimer un article
  delete: async (id) => {
    try {
      const response = await api.delete(`/articles/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la suppression de l'article ${id}:`, error);
      throw error;
    }
  }
};

// Services pour les magasins
export const magasinService = {
  getAll: async () => {
    try {
      const response = await api.get('/magasins');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des magasins:', error);
      throw error;
    }
  }
};

// Services pour les fournisseurs
export const fournisseurService = {
  getAll: async () => {
    try {
      const response = await api.get('/fournisseurs');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des fournisseurs:', error);
      throw error;
    }
  }
};

// Services pour les clients
export const clientService = {
  getAll: async () => {
    try {
      const response = await api.get('/clients');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des clients:', error);
      throw error;
    }
  }
};

// Services pour les codes de mouvement
export const codeMouvementService = {
  getAll: async () => {
    try {
      const response = await api.get('/codes-mouvement');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des codes de mouvement:', error);
      throw error;
    }
  }
};

// Services pour les stocks spéciaux
export const stockSpecialService = {
  getAll: async () => {
    try {
      const response = await api.get('/stocks-speciaux');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des stocks spéciaux:', error);
      throw error;
    }
  }
};
