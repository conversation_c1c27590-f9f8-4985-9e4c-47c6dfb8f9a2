:root {
  --sap-blue: #366092;
  --sap-light-blue: #5b9bd5;
  --sap-gray: #a5a5a5;
  --sap-dark-gray: #333;
  --sap-bg: #f0f0f0;
  --sap-border: #d1d1d1;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Arial', sans-serif;
  background-color: var(--sap-bg);
  color: var(--sap-dark-gray);
  font-size: 14px;
  line-height: 1.5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Layout */
.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.header {
  background-color: var(--sap-blue);
  color: white;
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
}

.nav ul {
  display: flex;
  list-style: none;
}

.nav li {
  margin-left: 20px;
}

.nav a {
  color: white;
  text-decoration: none;
  padding: 5px 10px;
  border-radius: 3px;
  transition: background-color 0.3s;
}

.nav a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.main {
  flex: 1;
  padding: 20px;
}

.footer {
  background-color: var(--sap-dark-gray);
  color: white;
  padding: 10px 20px;
  text-align: center;
}

/* Components */
.card {
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  background-color: var(--sap-light-blue);
  color: white;
  padding: 10px 15px;
  font-weight: bold;
}

.card-body {
  padding: 15px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-control {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid var(--sap-border);
  border-radius: 3px;
  font-size: 14px;
}

.btn {
  display: inline-block;
  padding: 8px 15px;
  background-color: var(--sap-light-blue);
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
  text-decoration: none;
  transition: background-color 0.3s;
}

.btn:hover {
  background-color: var(--sap-blue);
}

.btn-secondary {
  background-color: var(--sap-gray);
}

.btn-secondary:hover {
  background-color: #888;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid var(--sap-border);
}

.table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.table tr:hover {
  background-color: #f9f9f9;
}

/* Utilities */
.text-center {
  text-align: center;
}

.mt-1 {
  margin-top: 0.5rem;
}

.mt-2 {
  margin-top: 1rem;
}

.mb-1 {
  margin-bottom: 0.5rem;
}

.mb-2 {
  margin-bottom: 1rem;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.gap-1 {
  gap: 0.5rem;
}

.gap-2 {
  gap: 1rem;
}
